import { NextRequest, NextResponse } from 'next/server';
import { writeFile, readFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

interface SavedTemplate {
  id: string;
  name: string;
  originalTemplateId: string; // Required reference to original uploaded template
  savedFormData: any; // Store the form data from SOW generator
  createdAt: string;
  lastUsed?: string;
  usageCount: number;
}

// Get templates file path - use Railway storage path for consistency
function getTemplatesFilePath(): string {
  const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
  return join(storageRoot, 'saved-templates.json');
}

// Load existing templates
async function loadTemplates(): Promise<SavedTemplate[]> {
  try {
    const templatesPath = getTemplatesFilePath();
    const data = await readFile(templatesPath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    // File doesn't exist or is empty, return empty array
    return [];
  }
}

// Save templates to file
async function saveTemplates(templates: SavedTemplate[]): Promise<void> {
  const templatesPath = getTemplatesFilePath();
  const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');

  // Ensure storage directory exists
  try {
    await mkdir(storageRoot, { recursive: true });
  } catch (error) {
    // Directory might already exist
  }

  await writeFile(templatesPath, JSON.stringify(templates, null, 2));
  console.log('✅ Template saved to:', templatesPath);
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, formData, originalTemplateId, markdown } = body;

    console.log('💾 TEMPLATE SAVE: Request body keys:', Object.keys(body));
    console.log('💾 TEMPLATE SAVE: Has originalTemplateId:', !!originalTemplateId);
    console.log('💾 TEMPLATE SAVE: Has markdown:', !!markdown);

    if (!name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    if (!formData) {
      return NextResponse.json({ error: 'formData is required' }, { status: 400 });
    }

    // Check if we have originalTemplateId (new approach) or markdown (legacy approach)
    if (!originalTemplateId && !markdown) {
      return NextResponse.json({
        error: 'Either originalTemplateId or markdown is required'
      }, { status: 400 });
    }

    console.log('💾 SIMPLIFIED SAVE: Saving template with form data:', {
      name,
      originalTemplateId,
      hasMarkdown: !!markdown,
      hasFormData: !!formData
    });

    const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');

    // If we have originalTemplateId, verify the original template exists
    if (originalTemplateId) {
      const uploadsDir = join(storageRoot, 'uploads');
      const originalDocxPath = join(uploadsDir, `${originalTemplateId}.docx`);

      try {
        await readFile(originalDocxPath);
        console.log('✅ SIMPLIFIED SAVE: Original template verified:', originalTemplateId);
      } catch (error) {
        console.error('❌ SIMPLIFIED SAVE: Original template not found:', originalTemplateId);
        return NextResponse.json({ error: 'Original template not found' }, { status: 404 });
      }
    }

    // Load existing templates
    const templates = await loadTemplates();
    console.log('📂 SIMPLIFIED SAVE: Loaded existing templates:', templates.length);

    // Create new template - either simplified (with originalTemplateId) or legacy (with markdown)
    const newTemplate: SavedTemplate = {
      id: uuidv4(),
      name: name,
      originalTemplateId: originalTemplateId || 'legacy', // Use 'legacy' for markdown-based templates
      savedFormData: formData,
      createdAt: new Date().toISOString(),
      usageCount: 0
    };

    // If this is a legacy template (markdown-based), store the markdown
    if (markdown && !originalTemplateId) {
      (newTemplate as any).markdown = markdown;
      console.log('💾 SIMPLIFIED SAVE: Saving legacy template with markdown');
    }

    // Add to templates array
    templates.push(newTemplate);

    // Save back to file
    await saveTemplates(templates);
    console.log('✅ SIMPLIFIED SAVE: Template saved successfully:', newTemplate.id, 'referencing original template:', originalTemplateId);

    return NextResponse.json({
      id: newTemplate.id,
      message: 'Template saved successfully',
      originalTemplateId: originalTemplateId
    });

  } catch (error) {
    console.error('❌ Template save error:', error);
    return NextResponse.json(
      { error: 'Failed to save template' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    console.log('📂 SAVED TEMPLATES: Loading saved templates...');

    const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
    const templatesPath = getTemplatesFilePath();
    console.log('📂 SAVED TEMPLATES: Using storage directory:', storageRoot);
    console.log('🌍 SAVED TEMPLATES: STORAGE_PATH env var:', process.env.STORAGE_PATH || 'not set');
    console.log('🔍 SAVED TEMPLATES: Looking for templates at:', templatesPath);

    const templates = await loadTemplates();
    console.log('✅ SAVED TEMPLATES: Loaded templates:', templates.length);
    console.log('📋 SAVED TEMPLATES: Template IDs:', templates.map(t => t.id));

    // Sort by most recently used, then by creation date
    templates.sort((a, b) => {
      if (a.lastUsed && b.lastUsed) {
        return new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime();
      }
      if (a.lastUsed && !b.lastUsed) return -1;
      if (!a.lastUsed && b.lastUsed) return 1;
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    return NextResponse.json(templates);

  } catch (error) {
    console.error('❌ SAVED TEMPLATES: Template load error:', error);
    return NextResponse.json(
      { error: 'Failed to load templates' },
      { status: 500 }
    );
  }
}

// DELETE - Remove a saved template
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('id');

    if (!templateId) {
      console.log('❌ SAVED TEMPLATE DELETE: No template ID provided');
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    console.log('🗑️ SAVED TEMPLATE DELETE: Starting deletion of saved template:', templateId);

    // Load existing templates
    const templates = await loadTemplates();
    console.log('📂 SAVED TEMPLATE DELETE: Found templates before deletion:', templates.length);
    console.log('📋 SAVED TEMPLATE DELETE: Available template IDs:', templates.map(t => t.id));

    // Find the template to delete
    const templateToDelete = templates.find(t => t.id === templateId);

    // Filter out the template to delete
    const updatedTemplates = templates.filter(t => t.id !== templateId);

    if (updatedTemplates.length === templates.length) {
      console.log('❌ SAVED TEMPLATE DELETE: Template not found:', templateId);
      return NextResponse.json(
        { error: 'Saved template not found' },
        { status: 404 }
      );
    }

    console.log('🎯 SAVED TEMPLATE DELETE: Found template to delete:', templateToDelete?.name);

    // CRITICAL FIX: If this saved template references an original template, clean up UserDataService data too
    if (templateToDelete?.originalTemplateId && templateToDelete.originalTemplateId !== 'legacy') {
      try {
        const { unlink } = await import('fs/promises');
        const { join } = await import('path');
        const userDataDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'user-data') : join(process.cwd(), 'user-data');
        const userDataFile = join(userDataDir, `${templateToDelete.originalTemplateId}.json`);

        try {
          await unlink(userDataFile);
          console.log('🗑️ SAVED TEMPLATE DELETE: Cleaned up UserDataService data for original template:', templateToDelete.originalTemplateId);
        } catch (error) {
          console.log('⚠️ SAVED TEMPLATE DELETE: UserDataService file not found (expected):', userDataFile);
        }
      } catch (error) {
        console.warn('⚠️ SAVED TEMPLATE DELETE: Error cleaning UserDataService data:', error);
      }
    }

    // Save updated templates list
    try {
      await saveTemplates(updatedTemplates);
      console.log('✅ SAVED TEMPLATE DELETE: Template deleted successfully. Remaining templates:', updatedTemplates.length);
    } catch (error) {
      console.error('❌ SAVED TEMPLATE DELETE: Failed to save updated templates list:', error);
      return NextResponse.json(
        { error: 'Failed to update saved templates list after deletion' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Saved template deleted successfully',
      remainingCount: updatedTemplates.length,
      deletedTemplate: templateToDelete?.name
    });

  } catch (error) {
    console.error('❌ SAVED TEMPLATE DELETE: Failed to delete template:', error);
    return NextResponse.json(
      { error: 'Failed to delete saved template', details: String(error) },
      { status: 500 }
    );
  }
}
