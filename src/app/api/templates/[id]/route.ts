import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

interface SavedTemplate {
  id: string;
  name: string;
  originalTemplateId: string; // Required reference to original uploaded template
  savedFormData: any; // Store the form data from SOW generator
  createdAt: string;
  lastUsed?: string;
  usageCount: number;
}

async function loadTemplates(): Promise<SavedTemplate[]> {
  try {
    const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
    const templatesPath = join(storageRoot, 'saved-templates.json');
    const data = await readFile(templatesPath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      console.error('❌ SAVED TEMPLATE: No template ID provided');
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    console.log('🔍 SAVED TEMPLATE: Loading template:', id);

    const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
    console.log('📂 SAVED TEMPLATE: Using storage directory:', storageRoot);
    console.log('🌍 SAVED TEMPLATE: STORAGE_PATH env var:', process.env.STORAGE_PATH || 'not set');

    // Load all templates
    const templates = await loadTemplates();
    console.log('📂 SAVED TEMPLATE: Found templates:', templates.length);

    // Find the specific template
    const template = templates.find(t => t.id === id);

    if (!template) {
      console.error('❌ SAVED TEMPLATE: Template not found:', id);
      console.error('❌ SAVED TEMPLATE: Available template IDs:', templates.map(t => t.id));
      return NextResponse.json({
        error: 'Template not found',
        templateId: id,
        availableTemplates: templates.map(t => ({ id: t.id, name: t.name }))
      }, { status: 404 });
    }

    console.log('✅ SAVED TEMPLATE: Template found:', template.name);
    console.log('🔗 SAVED TEMPLATE: Has originalTemplateId:', !!template.originalTemplateId);

    // Update last used timestamp and usage count
    template.lastUsed = new Date().toISOString();
    template.usageCount = (template.usageCount || 0) + 1;

    // Save updated templates back to file (FIXED: Use correct storage path for Railway)
    try {
      const { writeFile } = await import('fs/promises');
      const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
      const templatesPath = join(storageRoot, 'saved-templates.json');
      console.log('💾 SAVED TEMPLATE: Updating usage stats at:', templatesPath);
      await writeFile(templatesPath, JSON.stringify(templates, null, 2));
      console.log('✅ SAVED TEMPLATE: Usage stats updated successfully');
    } catch (error) {
      console.warn('⚠️ SAVED TEMPLATE: Failed to update template usage stats:', error);
    }

    // SIMPLIFIED APPROACH: Load original template data and return with saved form data
    console.log('🔄 SIMPLIFIED LOAD: Loading original template data for:', template.originalTemplateId);

    try {
      // Load original template metadata from uploads directory
      const uploadsDir = join(storageRoot, 'uploads');
      const originalMetadataPath = join(uploadsDir, `${template.originalTemplateId}.json`);
      const originalMetadata = JSON.parse(await readFile(originalMetadataPath, 'utf-8'));

      // Return original template structure with saved form data attached
      const responseData = {
        id: template.id, // Use saved template ID
        name: template.name, // Use saved template name
        originalTemplateId: template.originalTemplateId,
        markdown: originalMetadata.originalMarkdown || originalMetadata.markdown, // Use original template's markdown
        placeholders: originalMetadata.placeholders || [], // Include original template's placeholders
        savedFormData: template.savedFormData, // Include saved form data
        createdAt: template.createdAt,
        lastUsed: template.lastUsed,
        usageCount: template.usageCount,
        type: 'saved' // Mark as saved template
      };

      console.log('✅ SIMPLIFIED LOAD: Loaded original template + form data, markdown length:', originalMetadata.originalMarkdown?.length || originalMetadata.markdown?.length);
      return NextResponse.json(responseData);

    } catch (originalError) {
      console.error('❌ SIMPLIFIED LOAD: Failed to load original template:', originalError);

      // Remove this corrupted saved template from the list
      try {
        const { writeFile } = await import('fs/promises');
        const updatedTemplates = templates.filter(t => t.id !== id);
        const templatesPath = join(storageRoot, 'saved-templates.json');
        await writeFile(templatesPath, JSON.stringify(updatedTemplates, null, 2));
        console.log('🧹 SIMPLIFIED LOAD: Removed corrupted saved template:', id);
      } catch (cleanupError) {
        console.warn('⚠️ SIMPLIFIED LOAD: Failed to cleanup corrupted template:', cleanupError);
      }

      return NextResponse.json({
        error: 'Original template not found. This saved template has been removed.',
        templateId: id,
        originalTemplateId: template.originalTemplateId,
        cleaned: true
      }, { status: 404 });
    }

  } catch (error) {
    console.error('Template retrieval error:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve template' },
      { status: 500 }
    );
  }
}
