import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, unlink } from 'fs/promises';
import { join } from 'path';

interface SavedSOW {
  id: string;
  title: string;
  clientName: string;
  projectName: string;
  templateName: string;
  templateId?: string;
  markdown: string;
  createdAt: string;
  status: 'draft' | 'final';
  fileSize?: number;
}

// GET - Get specific SOW details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const sowId = resolvedParams.id;

    if (!sowId) {
      return NextResponse.json(
        { error: 'SOW ID is required' },
        { status: 400 }
      );
    }

    // FIXED: Use consistent Railway storage path
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const sowFilePath = join(uploadsDir, `sow-${sowId}.json`);

    try {
      const sowData = await readFile(sowFilePath, 'utf-8');
      const sow: SavedSOW = JSON.parse(sowData);

      return NextResponse.json(sow);
    } catch (error) {
      return NextResponse.json(
        { error: 'SOW not found' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('SOW fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch SOW: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}

// PUT - Update a specific SOW
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const sowId = resolvedParams.id;

    if (!sowId) {
      return NextResponse.json(
        { error: 'SOW ID is required' },
        { status: 400 }
      );
    }

    const updatedSOWData = await request.json();

    const uploadsDir = join(process.cwd(), 'uploads');
    const sowFilePath = join(uploadsDir, `sow-${sowId}.json`);
    const sowsListPath = join(uploadsDir, 'sows.json');

    // Update individual SOW file
    try {
      const existingSOWData = await readFile(sowFilePath, 'utf-8');
      const existingSOW: SavedSOW = JSON.parse(existingSOWData);

      // Merge the updates with existing data
      const updatedSOW: SavedSOW = {
        ...existingSOW,
        ...updatedSOWData,
        id: sowId, // Ensure ID doesn't change
        createdAt: existingSOW.createdAt, // Preserve creation date
      };

      // Save updated SOW file
      await writeFile(sowFilePath, JSON.stringify(updatedSOW, null, 2));

      // Update SOWs list
      try {
        const sowsData = await readFile(sowsListPath, 'utf-8');
        let sowsList: SavedSOW[] = JSON.parse(sowsData);

        const sowIndex = sowsList.findIndex(sow => sow.id === sowId);
        if (sowIndex !== -1) {
          sowsList[sowIndex] = updatedSOW;
          await writeFile(sowsListPath, JSON.stringify(sowsList, null, 2));
        }
      } catch (error) {
        console.warn('Could not update SOWs list:', error);
      }

      console.log('SOW updated successfully:', sowId);

      return NextResponse.json(updatedSOW);

    } catch (error) {
      return NextResponse.json(
        { error: 'SOW not found' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('SOW update error:', error);
    return NextResponse.json(
      { error: 'Failed to update SOW: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}

// DELETE - Delete a specific SOW
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const sowId = resolvedParams.id;

    if (!sowId) {
      console.log('❌ SOW DELETE: No SOW ID provided');
      return NextResponse.json(
        { error: 'SOW ID is required' },
        { status: 400 }
      );
    }

    console.log('🗑️ SOW DELETE: Starting deletion of SOW:', sowId);

    // Use Railway storage path if available
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const sowFilePath = join(uploadsDir, `sow-${sowId}.json`);
    const sowsListPath = join(uploadsDir, 'sows.json');

    console.log('📁 SOW DELETE: Using uploads directory:', uploadsDir);
    console.log('📄 SOW DELETE: SOW file path:', sowFilePath);
    console.log('📋 SOW DELETE: SOWs list path:', sowsListPath);

    // Ensure uploads directory exists
    try {
      const { mkdir } = await import('fs/promises');
      await mkdir(uploadsDir, { recursive: true });
      console.log('📁 SOW DELETE: Ensured uploads directory exists');
    } catch (error) {
      console.log('📁 SOW DELETE: Directory creation result:', error);
    }

    // Remove from SOWs list
    try {
      const sowsData = await readFile(sowsListPath, 'utf-8');
      let sowsList: SavedSOW[] = JSON.parse(sowsData);

      console.log('📂 SOW DELETE: Found SOWs list with', sowsList.length, 'SOWs');
      console.log('📋 SOW DELETE: Available SOW IDs:', sowsList.map(sow => sow.id));

      const originalLength = sowsList.length;
      const sowToDelete = sowsList.find(sow => sow.id === sowId);
      sowsList = sowsList.filter(sow => sow.id !== sowId);

      if (sowsList.length === originalLength) {
        console.log('❌ SOW DELETE: SOW not found in list:', sowId);
        return NextResponse.json(
          { error: 'SOW not found in list' },
          { status: 404 }
        );
      }

      console.log('🎯 SOW DELETE: Found SOW to delete:', sowToDelete?.name);

      // Save updated list
      await writeFile(sowsListPath, JSON.stringify(sowsList, null, 2));
      console.log('✅ SOW DELETE: Updated SOWs list saved. Remaining SOWs:', sowsList.length);

      // Delete individual SOW file
      try {
        await unlink(sowFilePath);
        console.log('🗑️ SOW DELETE: Successfully deleted SOW file:', sowFilePath);
      } catch (error) {
        console.warn('⚠️ SOW DELETE: SOW file not found, but removed from list:', error);
      }

      console.log('✅ SOW DELETE: SOW deleted successfully:', sowId);

      return NextResponse.json({
        message: 'SOW deleted successfully',
        deletedSOW: sowToDelete?.name
      });

    } catch (error) {
      return NextResponse.json(
        { error: 'SOWs list not found' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('SOW deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete SOW: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}