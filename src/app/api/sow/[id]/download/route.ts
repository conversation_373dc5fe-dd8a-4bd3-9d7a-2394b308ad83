import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

interface SavedSOW {
  id: string;
  title: string;
  clientName: string;
  projectName: string;
  templateName: string;
  markdown: string;
  createdAt: string;
  status: 'draft' | 'final';
  fileSize?: number;
  templateId?: string;
}

// GET - Download SOW as DOCX
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const sowId = resolvedParams.id;
    
    if (!sowId) {
      return NextResponse.json(
        { error: 'SOW ID is required' },
        { status: 400 }
      );
    }

    // FIXED: Use consistent Railway storage path
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const sowFilePath = join(uploadsDir, `sow-${sowId}.json`);

    // Load SOW data
    let sowData: SavedSOW;
    try {
      const sowFileContent = await readFile(sowFilePath, 'utf-8');
      sowData = JSON.parse(sowFileContent);
    } catch (error) {
      return NextResponse.json(
        { error: 'SOW not found' },
        { status: 404 }
      );
    }

    // Find the template ID by looking for templates with matching name
    let templateId = sowData.templateId;
    
    if (!templateId) {
      // Try to find template by name
      try {
        const templatesListPath = join(uploadsDir, 'templates.json');
        const templatesData = await readFile(templatesListPath, 'utf-8');
        const templates = JSON.parse(templatesData);
        
        const matchingTemplate = templates.find((t: any) => t.name === sowData.templateName);
        if (matchingTemplate) {
          templateId = matchingTemplate.id;
        }
      } catch (error) {
        console.warn('Could not find template for SOW:', error);
      }
    }

    if (!templateId) {
      return NextResponse.json(
        { error: 'Template not found for this SOW. Cannot regenerate DOCX.' },
        { status: 404 }
      );
    }

    // Convert markdown to DOCX using pandoc with template as reference
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const { writeFile, readFile, unlink } = require('fs/promises');
      const { join } = require('path');

      const execAsync = promisify(exec);
      const uploadsDir = join(process.cwd(), 'uploads');
      const originalDocxPath = join(uploadsDir, `${templateId}.docx`);
      const tempMarkdownPath = join(uploadsDir, `temp-download-${sowId}-${Date.now()}.md`);
      const tempDocxPath = join(uploadsDir, `temp-download-${sowId}-${Date.now()}.docx`);

      // Write SOW markdown to temp file
      await writeFile(tempMarkdownPath, sowData.markdown, 'utf-8');

      // Convert markdown to DOCX using original template as reference for perfect formatting
      const pandocCommand = `pandoc "${tempMarkdownPath}" -f markdown -t docx --reference-doc="${originalDocxPath}" -o "${tempDocxPath}"`;

      console.log('Converting SOW to DOCX with pandoc:', pandocCommand);
      await execAsync(pandocCommand);

      // Read the generated DOCX
      const docxBuffer = await readFile(tempDocxPath);

      // Clean up temp files
      try {
        await unlink(tempMarkdownPath);
        await unlink(tempDocxPath);
      } catch (cleanupError) {
        console.warn('Failed to clean up temp files:', cleanupError);
      }
      
      if (docxBuffer.byteLength === 0) {
        throw new Error('Generated DOCX file is empty');
      }

      // Generate filename
      const filename = `${sowData.title.replace(/[^a-zA-Z0-9\s-]/g, '').replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.docx`;

      // Return the DOCX file
      return new NextResponse(docxBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': docxBuffer.byteLength.toString(),
        },
      });

    } catch (conversionError) {
      console.error('SOW conversion error:', conversionError);
      return NextResponse.json(
        { error: 'Failed to generate DOCX: ' + (conversionError instanceof Error ? conversionError.message : 'Unknown error') },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('SOW download error:', error);
    return NextResponse.json(
      { error: 'Failed to download SOW: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
