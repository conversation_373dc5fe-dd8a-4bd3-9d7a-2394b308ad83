import { NextRequest, NextResponse } from 'next/server';
import { readdir, readFile, stat } from 'fs/promises';
import { join } from 'path';

// GET - Debug template storage to understand what files exist
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 TEMPLATE STORAGE DEBUG: Starting comprehensive storage analysis...');
    
    const diagnostics: any = {
      environment: {
        STORAGE_PATH: process.env.STORAGE_PATH || 'not set',
        NODE_ENV: process.env.NODE_ENV || 'not set',
        cwd: process.cwd()
      },
      storage: {},
      savedTemplates: [],
      uploadedTemplates: [],
      errors: []
    };

    // Check storage paths
    const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    const templatesDir = join(storageRoot, 'templates');
    
    console.log('🔍 Storage paths:');
    console.log('  Storage root:', storageRoot);
    console.log('  Uploads dir:', uploadsDir);
    console.log('  Templates dir:', templatesDir);

    // Check saved templates
    try {
      const savedTemplatesPath = join(storageRoot, 'saved-templates.json');
      const savedTemplatesData = await readFile(savedTemplatesPath, 'utf-8');
      const savedTemplates = JSON.parse(savedTemplatesData);
      
      diagnostics.savedTemplates = savedTemplates.map((t: any) => ({
        id: t.id,
        name: t.name,
        createdAt: t.createdAt,
        hasMarkdown: !!t.markdown,
        markdownLength: t.markdown?.length || 0
      }));
      
      console.log('✅ Found', savedTemplates.length, 'saved templates');
    } catch (error) {
      diagnostics.errors.push(`Saved templates error: ${error}`);
      console.log('❌ Saved templates error:', error);
    }

    // Check uploads directory
    try {
      const uploadsFiles = await readdir(uploadsDir);
      const docxFiles = uploadsFiles.filter(f => f.endsWith('.docx'));
      const jsonFiles = uploadsFiles.filter(f => f.endsWith('.json'));
      
      diagnostics.storage.uploads = {
        exists: true,
        totalFiles: uploadsFiles.length,
        docxFiles: docxFiles,
        jsonFiles: jsonFiles,
        allFiles: uploadsFiles
      };
      
      console.log('✅ Uploads directory:', uploadsFiles.length, 'files');
      console.log('  DOCX files:', docxFiles);
      console.log('  JSON files:', jsonFiles);
    } catch (error) {
      diagnostics.storage.uploads = { exists: false, error: String(error) };
      console.log('❌ Uploads directory error:', error);
    }

    // Check templates directory and subdirectories
    try {
      const templatesFiles = await readdir(templatesDir);
      diagnostics.storage.templates = {
        exists: true,
        subdirectories: []
      };
      
      for (const item of templatesFiles) {
        const itemPath = join(templatesDir, item);
        const itemStat = await stat(itemPath);
        
        if (itemStat.isDirectory()) {
          try {
            const subFiles = await readdir(itemPath);
            const subDocxFiles = subFiles.filter(f => f.endsWith('.docx'));
            
            diagnostics.storage.templates.subdirectories.push({
              name: item,
              files: subFiles,
              docxFiles: subDocxFiles
            });
            
            console.log(`✅ Templates/${item}:`, subFiles.length, 'files, DOCX:', subDocxFiles);
          } catch (subError) {
            console.log(`❌ Templates/${item} error:`, subError);
          }
        }
      }
    } catch (error) {
      diagnostics.storage.templates = { exists: false, error: String(error) };
      console.log('❌ Templates directory error:', error);
    }

    // Check for specific template IDs mentioned in logs
    const testTemplateIds = [
      '244cc1b6-af07-4c70-8c73-4dbc45c51499',
      '503a73fa-3bb6-4eea-b860-04f895173b2a',
      'b52e7b3b-da80-466a-91ba-611132ee880c'
    ];

    diagnostics.templateSearch = {};
    
    for (const templateId of testTemplateIds) {
      const searchResults = {
        id: templateId,
        foundLocations: []
      };

      // Check uploads
      try {
        const uploadsPath = join(uploadsDir, `${templateId}.docx`);
        await readFile(uploadsPath);
        searchResults.foundLocations.push('uploads');
      } catch {}

      // Check templates subdirectories
      try {
        const templatesFiles = await readdir(templatesDir);
        for (const subdir of templatesFiles) {
          try {
            const subdirPath = join(templatesDir, subdir, `${templateId}.docx`);
            await readFile(subdirPath);
            searchResults.foundLocations.push(`templates/${subdir}`);
          } catch {}
        }
      } catch {}

      diagnostics.templateSearch[templateId] = searchResults;
      console.log(`🔍 Template ${templateId} found in:`, searchResults.foundLocations);
    }

    return NextResponse.json(diagnostics, { status: 200 });

  } catch (error) {
    console.error('❌ TEMPLATE STORAGE DEBUG: Error:', error);
    return NextResponse.json(
      { error: 'Debug failed', details: String(error) },
      { status: 500 }
    );
  }
}
