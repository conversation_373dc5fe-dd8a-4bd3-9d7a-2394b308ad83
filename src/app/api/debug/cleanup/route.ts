import { NextRequest, NextResponse } from 'next/server';
import { readdir, unlink, readFile, writeFile } from 'fs/promises';
import { join } from 'path';

export async function POST(request: NextRequest) {
  try {
    console.log('🧹 CLEANUP: Starting storage cleanup...');
    
    // Get storage root from environment
    const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
    const uploadsDir = join(storageRoot, 'uploads');
    const savedTemplatesPath = join(storageRoot, 'saved-templates.json');
    
    let cleanupResults = {
      savedTemplatesCleared: false,
      orphanedFilesRemoved: 0,
      errors: []
    };

    // 1. Clear saved templates file
    try {
      await writeFile(savedTemplatesPath, JSON.stringify([], null, 2));
      cleanupResults.savedTemplatesCleared = true;
      console.log('✅ CLEANUP: Cleared saved-templates.json');
    } catch (error) {
      console.log('ℹ️ CLEANUP: No saved-templates.json to clear');
    }

    // 2. List and optionally remove orphaned files
    try {
      const files = await readdir(uploadsDir);
      console.log('📁 CLEANUP: Found files in uploads:', files);
      
      // For now, just report what's there - don't delete automatically
      cleanupResults.orphanedFilesRemoved = files.length;
      
    } catch (error) {
      console.log('ℹ️ CLEANUP: No uploads directory found');
    }

    console.log('✅ CLEANUP: Storage cleanup complete');
    
    return NextResponse.json({
      success: true,
      message: 'Storage cleanup completed',
      results: cleanupResults
    });

  } catch (error) {
    console.error('❌ CLEANUP: Failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Cleanup failed: ' + (error instanceof Error ? error.message : 'Unknown error') 
      },
      { status: 500 }
    );
  }
}
