import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, unlink, mkdir } from 'fs/promises';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// GET - Serve template preview (original DOCX or generated from saved template)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const templateId = resolvedParams.id;

    if (!templateId) {
      return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
    }

    console.log('🔍 TEMPLATE PREVIEW: Looking up template:', templateId);

    const storageRoot = process.env.STORAGE_PATH || process.cwd();
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');

    // STEP 1: Try to find original uploaded DOCX file
    const docxPath = join(uploadsDir, `${templateId}.docx`);
    
    try {
      const docxBuffer = await readFile(docxPath);
      console.log('✅ TEMPLATE PREVIEW: Found original DOCX file, size:', docxBuffer.length, 'bytes');
      
      // Get filename from metadata
      let filename = `template-${templateId}.docx`;
      try {
        const metadataPath = join(uploadsDir, `${templateId}.json`);
        const metadata = JSON.parse(await readFile(metadataPath, 'utf-8'));
        filename = metadata.name || filename;
      } catch (e) {
        // Use default filename
      }

      return new NextResponse(docxBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': `inline; filename="${filename}"`,
          'Content-Length': docxBuffer.length.toString(),
        },
      });

    } catch (docxError) {
      console.log('📄 SIMPLIFIED PREVIEW: No original DOCX found, checking saved templates...');

      // STEP 2: Check if this is a saved template and get original DOCX
      try {
        const savedTemplatesPath = join(storageRoot, 'saved-templates.json');
        const savedTemplatesData = await readFile(savedTemplatesPath, 'utf-8');
        const savedTemplates = JSON.parse(savedTemplatesData);

        const savedTemplate = savedTemplates.find((t: any) => t.id === templateId);

        if (!savedTemplate) {
          console.log('❌ SIMPLIFIED PREVIEW: Template not found in any storage system');
          return NextResponse.json({ error: 'Template not found' }, { status: 404 });
        }

        console.log('✅ SIMPLIFIED PREVIEW: Found saved template:', savedTemplate.name);

        // SIMPLIFIED: Always serve the original DOCX file for saved templates
        if (savedTemplate.originalTemplateId) {
          console.log('🔄 SIMPLIFIED PREVIEW: Loading original DOCX for saved template:', savedTemplate.originalTemplateId);

          const originalDocxPath = join(uploadsDir, `${savedTemplate.originalTemplateId}.docx`);

          try {
            const originalDocxBuffer = await readFile(originalDocxPath);
            console.log('✅ SIMPLIFIED PREVIEW: Serving original DOCX, size:', originalDocxBuffer.length, 'bytes');

            // Get filename from original template metadata
            let filename = `${savedTemplate.name}.docx`;
            try {
              const originalMetadataPath = join(uploadsDir, `${savedTemplate.originalTemplateId}.json`);
              const originalMetadata = JSON.parse(await readFile(originalMetadataPath, 'utf-8'));
              filename = originalMetadata.name || filename;
            } catch (e) {
              // Use saved template name as fallback
            }

            return new NextResponse(originalDocxBuffer, {
              headers: {
                'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'Content-Disposition': `inline; filename="${filename}"`,
                'Content-Length': originalDocxBuffer.length.toString(),
              },
            });

          } catch (originalDocxError) {
            console.error('❌ SIMPLIFIED PREVIEW: Original DOCX not found for saved template:', originalDocxError);
            return NextResponse.json({
              error: 'Original template file not found',
              originalTemplateId: savedTemplate.originalTemplateId
            }, { status: 404 });
          }
        }

        // If no originalTemplateId, this is a legacy saved template - not supported in simplified system
        if (!savedTemplate.originalTemplateId) {
          console.error('❌ SIMPLIFIED PREVIEW: Legacy saved template without originalTemplateId not supported');
          return NextResponse.json({
            error: 'Legacy template format not supported. Please re-save this template.'
          }, { status: 400 });
        }

        // Try to serve the original DOCX file
        const originalDocxPath = join(uploadsDir, `${savedTemplate.originalTemplateId}.docx`);

        try {
          const originalDocxBuffer = await readFile(originalDocxPath);
          console.log('✅ SIMPLIFIED PREVIEW: Found original DOCX, size:', originalDocxBuffer.length, 'bytes');

          // Get filename from original template metadata
          let filename = `template-${savedTemplate.originalTemplateId}.docx`;
          try {
            const originalMetadataPath = join(uploadsDir, `${savedTemplate.originalTemplateId}.json`);
            const originalMetadata = JSON.parse(await readFile(originalMetadataPath, 'utf-8'));
            filename = originalMetadata.name || filename;
          } catch (e) {
            // Use saved template name as fallback
            filename = `${savedTemplate.name}.docx`;
          }

          return new NextResponse(originalDocxBuffer, {
            headers: {
              'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              'Content-Disposition': `inline; filename="${filename}"`,
              'Content-Length': originalDocxBuffer.length.toString(),
            },
          });

        } catch (docxError) {
          console.error('❌ SIMPLIFIED PREVIEW: Original DOCX not found:', docxError);

          // Clean up this corrupted saved template
          try {
            const { writeFile } = await import('fs/promises');
            const updatedTemplates = savedTemplates.filter(t => t.id !== templateId);
            const savedTemplatesPath = join(storageRoot, 'saved-templates.json');
            await writeFile(savedTemplatesPath, JSON.stringify(updatedTemplates, null, 2));
            console.log('🧹 SIMPLIFIED PREVIEW: Removed corrupted saved template:', templateId);
          } catch (cleanupError) {
            console.warn('⚠️ SIMPLIFIED PREVIEW: Failed to cleanup corrupted template:', cleanupError);
          }

          return NextResponse.json({
            error: 'Original template file not found. This saved template has been removed.',
            templateId: templateId,
            originalTemplateId: savedTemplate.originalTemplateId,
            cleaned: true
          }, { status: 404 });
        }



      } catch (savedTemplateError) {
        console.error('❌ TEMPLATE PREVIEW: Saved template processing failed:', savedTemplateError);
        return NextResponse.json(
          { error: 'Failed to process template' },
          { status: 500 }
        );
      }
    }

  } catch (error) {
    console.error('❌ TEMPLATE PREVIEW: Unexpected error:', error);
    return NextResponse.json(
      { error: 'Failed to load template preview: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
