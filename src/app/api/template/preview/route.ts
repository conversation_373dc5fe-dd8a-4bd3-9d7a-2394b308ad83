import { NextRequest, NextResponse } from 'next/server';
import { readFile, unlink } from 'fs/promises';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// GET - Get template preview (markdown content)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('id');

    console.log('🔍 TEMPLATE PREVIEW: Loading template:', templateId);

    if (!templateId) {
      console.error('❌ TEMPLATE PREVIEW: No template ID provided');
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // STEP 1: Check saved templates first (using correct path)
    try {
      const storageRoot = process.env.STORAGE_PATH || join(process.cwd(), 'storage');
      const savedTemplatesPath = join(storageRoot, 'saved-templates.json');
      console.log('🔍 TEMPLATE PREVIEW: Checking saved templates at:', savedTemplatesPath);

      const savedTemplatesData = await readFile(savedTemplatesPath, 'utf-8');
      const savedTemplates = JSON.parse(savedTemplatesData);
      const savedTemplate = savedTemplates.find((t: any) => t.id === templateId);

      if (savedTemplate && savedTemplate.originalTemplateId) {
        console.log('✅ TEMPLATE PREVIEW: Found saved template:', savedTemplate.name);
        console.log('🔄 TEMPLATE PREVIEW: Using original template ID:', savedTemplate.originalTemplateId);

        // Load the original template metadata and convert using pandoc
        const uploadsDir = join(storageRoot, 'uploads');
        const originalMetadataPath = join(uploadsDir, `${savedTemplate.originalTemplateId}.json`);

        try {
          const originalMetadata = JSON.parse(await readFile(originalMetadataPath, 'utf-8'));
          console.log('✅ TEMPLATE PREVIEW: Loaded original template metadata');

          return NextResponse.json({
            id: templateId,
            name: savedTemplate.name || originalMetadata.name,
            markdown: originalMetadata.originalMarkdown,
            fields: originalMetadata.placeholders || [],
            uploadDate: savedTemplate.createdAt
          });
        } catch (originalError) {
          console.error('❌ TEMPLATE PREVIEW: Failed to load original template metadata:', originalError);
          throw new Error(`Original template not found: ${savedTemplate.originalTemplateId}`);
        }
      }
    } catch (savedTemplateError) {
      console.log('📝 TEMPLATE PREVIEW: No saved templates found, trying uploaded templates:', savedTemplateError);
    }

    // Fallback: Try uploaded templates system
    const uploadsDir = process.env.STORAGE_PATH ? join(process.env.STORAGE_PATH, 'uploads') : join(process.cwd(), 'uploads');
    console.log('📂 TEMPLATE PREVIEW: Using uploads directory:', uploadsDir);
    console.log('🌍 TEMPLATE PREVIEW: STORAGE_PATH env var:', process.env.STORAGE_PATH || 'not set');

    // Ensure uploads directory exists (Railway deployment fix)
    try {
      const { mkdir } = await import('fs/promises');
      await mkdir(uploadsDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
      console.log('📁 TEMPLATE PREVIEW: Directory creation result:', error);
    }

    // Try to load from metadata first
    const metadataPath = join(uploadsDir, `${templateId}.json`);
    console.log('🔍 TEMPLATE PREVIEW: Checking metadata at:', metadataPath);

    try {
      const metadataContent = await readFile(metadataPath, 'utf-8');
      const metadata = JSON.parse(metadataContent);
      console.log('✅ TEMPLATE PREVIEW: Loaded metadata successfully');

      return NextResponse.json({
        id: templateId,
        name: metadata.name,
        markdown: metadata.originalMarkdown,
        fields: metadata.placeholders,
        uploadDate: metadata.uploadDate
      });
    } catch (metadataError) {
      console.log('⚠️ TEMPLATE PREVIEW: Metadata not found, trying DOCX fallback:', metadataError);
      // Fallback: convert DOCX to markdown on-the-fly
      const docxPath = join(uploadsDir, `${templateId}.docx`);
      console.log('🔍 TEMPLATE PREVIEW: Checking DOCX at:', docxPath);
      
      try {
        // Check if DOCX file exists
        console.log('🔍 TEMPLATE PREVIEW: Checking if DOCX file exists...');
        await readFile(docxPath);
        console.log('✅ TEMPLATE PREVIEW: DOCX file found, converting to markdown...');

        // Convert to markdown using pandoc
        const tempMarkdownPath = join(uploadsDir, `preview-${templateId}-${Date.now()}.md`);
        const pandocCommand = `pandoc "${docxPath}" -t markdown -o "${tempMarkdownPath}"`;
        console.log('🔄 TEMPLATE PREVIEW: Running pandoc command:', pandocCommand);

        await execAsync(pandocCommand);
        const markdown = await readFile(tempMarkdownPath, 'utf-8');
        console.log('✅ TEMPLATE PREVIEW: Markdown conversion successful');

        // Clean up temp file
        try {
          await unlink(tempMarkdownPath);
        } catch (error) {
          console.warn('Failed to clean up temp preview file:', error);
        }

        // Extract placeholders
        const placeholders = new Set<string>();
        const patterns = [
          /\{([A-Z_]+)\}/g,
          /\[([A-Z_]+)\]/g,
          /\$\{([A-Z_]+)\}/g,
          /\{\{([A-Z_]+)\}\}/g,
        ];

        patterns.forEach(pattern => {
          let match;
          while ((match = pattern.exec(markdown)) !== null) {
            placeholders.add(match[1]);
          }
        });

        console.log('✅ TEMPLATE PREVIEW: Extracted placeholders:', Array.from(placeholders));

        return NextResponse.json({
          id: templateId,
          name: `Template ${templateId}`,
          markdown: markdown,
          fields: Array.from(placeholders),
          uploadDate: new Date().toISOString()
        });

      } catch (docxError) {
        console.error('❌ TEMPLATE PREVIEW: DOCX file not found or conversion failed:', docxError);
        console.error('❌ TEMPLATE PREVIEW: Attempted paths:', { metadataPath, docxPath });
        return NextResponse.json(
          {
            error: 'Template file not found',
            details: `Could not find template files at ${metadataPath} or ${docxPath}`,
            templateId: templateId,
            uploadsDir: uploadsDir
          },
          { status: 404 }
        );
      }
    }

  } catch (error) {
    console.error('Template preview error:', error);
    return NextResponse.json(
      { error: 'Failed to load template preview: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
