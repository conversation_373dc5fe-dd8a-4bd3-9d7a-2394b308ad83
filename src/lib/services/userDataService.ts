import { SavedUserData } from '../types/template';

export interface TemplateUserData {
  templateId: string;
  userData: SavedUserData;
  lastSaved: Date;
  version: number;
}

export class UserDataService {
  private static readonly STORAGE_KEY = 'sow_template_user_data';
  private static readonly AUTOSAVE_INTERVAL = 2000; // 2 seconds
  private static autosaveTimers: Map<string, NodeJS.Timeout> = new Map();

  /**
   * Save user data for a specific template
   */
  static async saveUserData(templateId: string, userData: SavedUserData): Promise<void> {
    try {
      const existingData = this.getAllUserData();
      const templateData: TemplateUserData = {
        templateId,
        userData: {
          ...userData,
          lastSaved: new Date(),
          version: (existingData[templateId]?.version || 0) + 1
        },
        lastSaved: new Date(),
        version: (existingData[templateId]?.version || 0) + 1
      };

      existingData[templateId] = templateData;
      
      // Save to localStorage for client-side persistence
      if (typeof window !== 'undefined') {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(existingData));
      }

      // Also save to server for permanent storage
      await this.saveToServer(templateId, templateData);
      
      console.log(`✅ User data saved for template ${templateId}`);
    } catch (error) {
      console.error('Failed to save user data:', error);
      throw new Error('Failed to save user data');
    }
  }

  /**
   * Load user data for a specific template
   */
  static getUserData(templateId: string): SavedUserData | null {
    try {
      const allData = this.getAllUserData();
      return allData[templateId]?.userData || null;
    } catch (error) {
      console.error('Failed to load user data:', error);
      return null;
    }
  }

  /**
   * Check if user data exists for a template (without loading it)
   */
  static hasUserData(templateId: string): boolean {
    try {
      const allData = this.getAllUserData();
      return !!allData[templateId];
    } catch (error) {
      console.error('Failed to check user data existence:', error);
      return false;
    }
  }

  /**
   * Get all user data from localStorage
   */
  private static getAllUserData(): Record<string, TemplateUserData> {
    if (typeof window === 'undefined') return {};

    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.error('Failed to parse user data from localStorage:', error);
      return {};
    }
  }

  /**
   * Setup autosave for a template
   */
  static setupAutosave(templateId: string, getUserDataCallback: () => SavedUserData): void {
    // Clear existing autosave timer
    this.clearAutosave(templateId);

    const autosaveTimer = setInterval(async () => {
      try {
        const currentData = getUserDataCallback();
        if (this.hasDataChanged(templateId, currentData)) {
          await this.saveUserData(templateId, currentData);
          console.log(`🔄 Autosaved data for template ${templateId}`);
        }
      } catch (error) {
        console.error('Autosave failed:', error);
      }
    }, this.AUTOSAVE_INTERVAL);

    this.autosaveTimers.set(templateId, autosaveTimer);
  }

  /**
   * Clear autosave for a template
   */
  static clearAutosave(templateId: string): void {
    const timer = this.autosaveTimers.get(templateId);
    if (timer) {
      clearInterval(timer);
      this.autosaveTimers.delete(templateId);
    }
  }

  /**
   * Check if data has changed since last save
   */
  private static hasDataChanged(templateId: string, currentData: SavedUserData): boolean {
    const savedData = this.getUserData(templateId);
    if (!savedData) return true;

    // Simple comparison - in production, you might want a more sophisticated diff
    return JSON.stringify(currentData) !== JSON.stringify(savedData);
  }

  /**
   * Save data to server
   */
  private static async saveToServer(templateId: string, templateData: TemplateUserData): Promise<void> {
    try {
      const response = await fetch('/api/template/user-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          templateId,
          userData: templateData.userData
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save to server');
      }
    } catch (error) {
      console.warn('Server save failed, data saved locally only:', error);
      // Don't throw error - local save is still valid
    }
  }

  /**
   * Load data from server
   */
  static async loadFromServer(templateId: string): Promise<SavedUserData | null> {
    try {
      const response = await fetch(`/api/template/user-data?templateId=${templateId}`);
      if (response.ok) {
        const data = await response.json();
        return data.userData || null;
      }
    } catch (error) {
      console.warn('Server load failed, using local data:', error);
    }
    return null;
  }

  /**
   * Delete user data for a template
   */
  static async deleteUserData(templateId: string): Promise<void> {
    try {
      console.log(`🗑️ COMPREHENSIVE DELETE: Starting deletion of user data for template ${templateId}`);

      // Clear autosave
      this.clearAutosave(templateId);

      // Remove from localStorage
      const allData = this.getAllUserData();
      const hadLocalData = !!allData[templateId];
      delete allData[templateId];

      if (typeof window !== 'undefined') {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(allData));
        console.log(`🗑️ COMPREHENSIVE DELETE: Removed from localStorage (had data: ${hadLocalData})`);
      }

      // Remove from server
      const response = await fetch(`/api/template/user-data?templateId=${templateId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`🗑️ COMPREHENSIVE DELETE: Server deletion result:`, result.message);
      } else {
        console.warn(`⚠️ COMPREHENSIVE DELETE: Server deletion failed with status ${response.status}`);
      }

      console.log(`✅ COMPREHENSIVE DELETE: User data deletion completed for template ${templateId}`);
    } catch (error) {
      console.error('Failed to delete user data:', error);
      throw new Error('Failed to delete user data');
    }
  }

  /**
   * Get list of templates with saved user data
   */
  static getTemplatesWithSavedData(): string[] {
    const allData = this.getAllUserData();
    return Object.keys(allData);
  }

  /**
   * Check if template has saved user data
   */
  static hasUserData(templateId: string): boolean {
    const userData = this.getUserData(templateId);
    return userData !== null && Object.keys(userData).length > 0;
  }

  /**
   * Get a summary of saved user data for display
   */
  static getUserDataSummary(templateId: string): string {
    try {
      const userData = this.getUserData(templateId);
      if (!userData) return 'No saved data';

      const parts: string[] = [];
      if (userData.clientName) parts.push('Client');
      if (userData.projectName) parts.push('Project');
      if (userData.customFields?.contactName || userData.customFields?.companyName) parts.push('User Info');
      if (userData.totalBudget) parts.push('Budget');

      return parts.length > 0 ? `Saved: ${parts.join(', ')}` : 'Saved data available';
    } catch (error) {
      console.error('Failed to get user data summary:', error);
      return 'Error loading summary';
    }
  }

  /**
   * Get summary of saved data for display
   */
  static getUserDataSummary(templateId: string): string {
    const userData = this.getUserData(templateId);
    if (!userData) return 'No saved data';

    const fields = [];
    if (userData.clientName) fields.push('Client');
    if (userData.projectName) fields.push('Project');
    if (userData.totalBudget) fields.push('Budget');
    if (userData.startDate) fields.push('Timeline');

    return fields.length > 0 ? `Saved: ${fields.join(', ')}` : 'Partial data saved';
  }
}
